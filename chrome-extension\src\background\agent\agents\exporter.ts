import { z } from 'zod';
import { BaseAgent, type BaseAgentOptions, type ExtraAgentOptions } from './base';
import { createLogger } from '@src/background/log';
import { type AgentOutput } from '../types';
import { Actors, ExecutionState } from '../event/types';
import { DataExportService, type ExportOptions } from '../../services/dataExportService';

const logger = createLogger('ExporterAgent');

// Define Zod schema for exporter output
export const exporterOutputSchema = z.object({
  analysis: z.object({
    data_available: z.boolean(),
    data_count: z.number(),
    data_type: z.enum(['tabular', 'list', 'nested', 'mixed']),
    user_intent: z.enum(['export', 'chart', 'pdf', 'table', 'unknown']),
    recommended_action: z.string(),
  }),
  action: z.object({
    type: z.enum(['export', 'chart', 'pdf', 'table', 'error']),
    format: z.enum(['csv', 'json', 'excel', 'bar_chart', 'line_chart', 'pie_chart', 'report', 'interactive_table']),
    options: z.object({
      filename: z.string(),
      title: z.string().optional(),
      chart_type: z.enum(['bar', 'line', 'pie', 'doughnut', 'radar', 'scatter', 'heatmap', 'treemap', '3d']).optional(),
      library: z.enum(['chartjs', 'echarts', 'apexcharts']).optional(),
      include_metadata: z.boolean(),
    }),
    parameters: z.object({
      x_axis: z.string().optional(),
      y_axis: z.string().optional(),
      group_by: z.string().optional(),
      aggregation: z.enum(['sum', 'count', 'avg', 'max', 'min']).optional(),
      color_scheme: z.string().optional(),
      layout: z.enum(['vertical', 'horizontal', 'grid']).optional(),
    }),
  }),
  message: z.string(),
});

export type ExporterOutput = z.infer<typeof exporterOutputSchema>;

export interface ExportResult {
  success: boolean;
  content?: string;
  filename: string;
  mimeType?: string;
  downloadUrl?: string;
  size?: number;
  error?: string;
  chart_data?: any;
  pdf_data?: Uint8Array;
}

export class ExporterAgent extends BaseAgent<typeof exporterOutputSchema, ExporterOutput> {
  private exportService: DataExportService;

  constructor(options: BaseAgentOptions, extraOptions?: Partial<ExtraAgentOptions>) {
    super(exporterOutputSchema, options, { ...extraOptions, id: 'exporter' });
    this.exportService = new DataExportService();
  }

  /**
   * Check if the user request contains export intent
   */
  shouldActivate(userMessage: string): boolean {
    const exportKeywords = [
      'export', 'download', 'save as', 'convert to', 'generate file',
      'csv', 'json', 'excel', 'xlsx', 'txt', 'text file', 'spreadsheet',
      'save data', 'export data', 'download data', 'chart', 'graph', 
      'visualize', 'plot', 'report', 'pdf', 'table', 'grid'
    ];

    const message = userMessage.toLowerCase();
    return exportKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * Execute export process with enhanced capabilities
   */
  async execute(): Promise<AgentOutput<ExporterOutput>> {
    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_START, 'Analyzing export request...');

      const systemMessage = this.prompt.getSystemMessage();
      const userMessage = await this.prompt.getUserMessage(this.context);
      const inputMessages = [systemMessage, userMessage];

      const modelOutput = await this.invoke(inputMessages);
      if (!modelOutput) {
        throw new Error('Failed to get export analysis');
      }

      logger.info('Exporter analysis', JSON.stringify(modelOutput, null, 2));

      // Execute the determined action
      const result = await this.executeAction(modelOutput);

      if (result.success) {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_OK, modelOutput.message);
      } else {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_FAIL, result.error || 'Export failed');
      }

      return {
        id: this.id,
        result: modelOutput,
      };

    } catch (error) {
      const errorMsg = `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error('Export execution failed', error);
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.STEP_FAIL, errorMsg);

      return {
        id: this.id,
        error: errorMsg,
      };
    }
  }

  /**
   * Execute the determined action based on model output
   */
  private async executeAction(modelOutput: ExporterOutput): Promise<ExportResult> {
    const { action, analysis } = modelOutput;

    try {
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, `Executing ${action.type} action...`);

      switch (action.type) {
        case 'export':
          return await this.handleDataExport(action, analysis);
        
        case 'chart':
          return await this.handleChartGeneration(action, analysis);
        
        case 'pdf':
          return await this.handlePDFGeneration(action, analysis);
        
        case 'table':
          return await this.handleTableGeneration(action, analysis);
        
        case 'error':
          return {
            success: false,
            filename: 'error',
            error: modelOutput.message,
          };
        
        default:
          return {
            success: false,
            filename: 'unknown_action',
            error: `Unknown action type: ${action.type}`,
          };
      }
    } catch (error) {
      return {
        success: false,
        filename: 'execution_error',
        error: error instanceof Error ? error.message : 'Unknown execution error',
      };
    }
  }

  /**
   * Handle traditional data export (CSV, JSON, Excel)
   */
  private async handleDataExport(action: ExporterOutput['action'], analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    const extractedData = this.getExtractedData();
    if (!extractedData) {
      return {
        success: false,
        filename: 'no_data',
        error: 'No data available for export',
      };
    }

    const exportOptions: ExportOptions = {
      format: action.format as 'csv' | 'json' | 'excel',
      filename: action.options.filename,
      includeMetadata: action.options.include_metadata,
    };

    const result = await this.exportService.exportData(extractedData, exportOptions);
    this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Data exported to ${action.format.toUpperCase()}`);
    
    return {
      success: result.success,
      filename: result.filename,
      downloadUrl: result.downloadUrl,
      size: result.size,
      error: result.error,
    };
  }

  /**
   * Handle chart generation using available libraries
   */
  private async handleChartGeneration(action: ExporterOutput['action'], analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    const extractedData = this.getExtractedData();
    if (!extractedData) {
      return {
        success: false,
        filename: 'no_data',
        error: 'No data available for chart generation',
      };
    }

    try {
      // Load the appropriate chart library
      const chartLibrary = await this.loadChartLibrary(action.options.library || 'chartjs');
      
      // Process data for charting
      const chartData = this.processDataForChart(extractedData.data, action.parameters);
      
      // Generate chart configuration
      const chartConfig = this.generateChartConfig(chartData, action);
      
      // Create chart and export as image/HTML
      const chartResult = await this.createChart(chartLibrary, chartConfig, action);

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Chart generated using ${action.options.library}`);
      
      return {
        success: true,
        filename: action.options.filename,
        chart_data: chartResult,
        mimeType: 'text/html',
      };
    } catch (error) {
      return {
        success: false,
        filename: 'chart_error',
        error: error instanceof Error ? error.message : 'Chart generation failed',
      };
    }
  }

  /**
   * Handle PDF report generation
   */
  private async handlePDFGeneration(action: ExporterOutput['action'], analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    try {
      // Load PDF library
      const pdfLib = await this.loadPDFLibrary();
      
      // Generate PDF document
      const pdfDoc = await this.createPDFReport(pdfLib, action, analysis);
      
      // Convert to bytes
      const pdfBytes = await pdfDoc.save();

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'PDF report generated');
      
      return {
        success: true,
        filename: action.options.filename,
        pdf_data: pdfBytes,
        mimeType: 'application/pdf',
        size: pdfBytes.length,
      };
    } catch (error) {
      return {
        success: false,
        filename: 'pdf_error',
        error: error instanceof Error ? error.message : 'PDF generation failed',
      };
    }
  }

  /**
   * Handle interactive table generation
   */
  private async handleTableGeneration(action: ExporterOutput['action'], analysis: ExporterOutput['analysis']): Promise<ExportResult> {
    const extractedData = this.getExtractedData();
    if (!extractedData) {
      return {
        success: false,
        filename: 'no_data',
        error: 'No data available for table generation',
      };
    }

    try {
      // Load GridJS library
      const gridJS = await this.loadGridJSLibrary();
      
      // Generate interactive table HTML
      const tableHTML = this.generateInteractiveTable(gridJS, extractedData.data, action);

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, 'Interactive table generated');
      
      return {
        success: true,
        filename: action.options.filename,
        content: tableHTML,
        mimeType: 'text/html',
      };
    } catch (error) {
      return {
        success: false,
        filename: 'table_error',
        error: error instanceof Error ? error.message : 'Table generation failed',
      };
    }
  }

  /**
   * Get extracted data with flexible detection
   */
  private getExtractedData(): any | null {
    const extractedData = this.context.lastExtractionResult;
    
    if (!extractedData) return null;
    
    // Method 1: Standard format with .data property
    if (extractedData.data && Array.isArray(extractedData.data) && extractedData.data.length > 0) {
      return extractedData;
    }
    
    // Method 2: Direct array
    if (Array.isArray(extractedData) && extractedData.length > 0) {
      return {
        success: true,
        data: extractedData,
        metadata: {
          source_url: 'AI_GENERATED',
          extraction_timestamp: Date.now(),
          total_items: extractedData.length,
          extraction_method: 'direct_array',
          confidence_score: 0.8,
        },
      };
    }
    
    return null;
  }

  /**
   * Load chart library dynamically
   */
  private async loadChartLibrary(library: string): Promise<any> {
    const libraryPaths = {
      chartjs: 'node_modules/@lib/chart.min.js',
      echarts: 'node_modules/@lib/echarts.js',
      apexcharts: 'node_modules/@lib/apexcharts.min.js',
    };

    const path = libraryPaths[library as keyof typeof libraryPaths];
    if (!path) {
      throw new Error(`Unknown chart library: ${library}`);
    }

    const libraryPath = chrome.runtime.getURL(path);
    const response = await fetch(libraryPath);
    const libraryCode = await response.text();
    
    // Evaluate the library code
    eval(libraryCode);
    
    // Return the global object based on library
    switch (library) {
      case 'chartjs':
        return (globalThis as any).Chart;
      case 'echarts':
        return (globalThis as any).echarts;
      case 'apexcharts':
        return (globalThis as any).ApexCharts;
      default:
        throw new Error(`Failed to load library: ${library}`);
    }
  }

  /**
   * Load PDF library
   */
  private async loadPDFLibrary(): Promise<any> {
    const pdfLibPath = chrome.runtime.getURL('node_modules/@lib/pdf-lib.min.js');
    const response = await fetch(pdfLibPath);
    const pdfLibCode = await response.text();
    
    eval(pdfLibCode);
    return (globalThis as any).PDFLib;
  }

  /**
   * Load GridJS library
   */
  private async loadGridJSLibrary(): Promise<any> {
    const gridJSPath = chrome.runtime.getURL('node_modules/@lib/gridjs.umd.js');
    const response = await fetch(gridJSPath);
    const gridJSCode = await response.text();
    
    eval(gridJSCode);
    return (globalThis as any).gridjs;
  }

  /**
   * Process data for chart generation
   */
  private processDataForChart(data: any[], parameters: ExporterOutput['action']['parameters']): any {
    // Implementation for data processing based on chart requirements
    // This would analyze the data structure and prepare it for charting
    return {
      labels: data.map((item, index) => parameters.x_axis ? item[parameters.x_axis] : `Item ${index + 1}`),
      datasets: [{
        label: parameters.y_axis || 'Values',
        data: data.map(item => parameters.y_axis ? item[parameters.y_axis] : Object.keys(item).length),
      }],
    };
  }

  /**
   * Generate chart configuration
   */
  private generateChartConfig(chartData: any, action: ExporterOutput['action']): any {
    // Implementation for generating chart configuration based on library and type
    return {
      type: action.options.chart_type || 'bar',
      data: chartData,
      options: {
        responsive: true,
        plugins: {
          title: {
            display: true,
            text: action.options.title || 'Data Visualization',
          },
        },
      },
    };
  }

  /**
   * Create chart using loaded library
   */
  private async createChart(library: any, config: any, action: ExporterOutput['action']): Promise<any> {
    // Implementation for creating chart with the specific library
    // This would generate the chart and return HTML or image data
    return {
      html: `<div>Chart generated with ${action.options.library}</div>`,
      config: config,
    };
  }

  /**
   * Create PDF report
   */
  private async createPDFReport(pdfLib: any, action: ExporterOutput['action'], analysis: ExporterOutput['analysis']): Promise<any> {
    const { PDFDocument, StandardFonts, rgb } = pdfLib;
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    page.drawText(action.options.title || 'Data Export Report', {
      x: 50,
      y: 750,
      size: 20,
      font: font,
      color: rgb(0, 0, 0),
    });

    page.drawText(`Data items: ${analysis.data_count}`, {
      x: 50,
      y: 700,
      size: 12,
      font: font,
      color: rgb(0, 0, 0),
    });

    return pdfDoc;
  }

  /**
   * Generate interactive table HTML
   */
  private generateInteractiveTable(gridJS: any, data: any[], action: ExporterOutput['action']): string {
    // Implementation for generating interactive table HTML
    return `
      <div id="grid-table"></div>
      <script>
        new gridjs.Grid({
          columns: ${JSON.stringify(Object.keys(data[0] || {}))},
          data: ${JSON.stringify(data)},
          pagination: true,
          search: true,
          sort: true,
        }).render(document.getElementById('grid-table'));
      </script>
    `;
  }
}
