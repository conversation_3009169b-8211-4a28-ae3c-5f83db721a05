import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import type { AgentContext } from '../types';
import { Actors, ExecutionState } from '../event/types';
import { createLogger } from '../../log';
import { DataExportService, type ExportOptions } from '../../services/dataExportService';

const logger = createLogger('ExporterAgent');

export interface ExportRequest {
  data: any[];
  format: 'csv' | 'json' | 'excel';
  filename?: string;
  metadata?: any;
}

export interface ExportResult {
  success: boolean;
  content?: string;
  filename: string;
  mimeType?: string;
  downloadUrl?: string;
  size?: number;
  error?: string;
}

export class ExporterAgent {
  private context: AgentContext;
  private llm: BaseChatModel;
  private exportService: DataExportService;

  constructor(context: AgentContext, llm: BaseChatModel) {
    this.context = context;
    this.llm = llm;
    this.exportService = new DataExportService();
  }

  /**
   * Check if the user request contains export intent
   */
  shouldActivate(userMessage: string): boolean {
    const exportKeywords = [
      'export', 'download', 'save as', 'convert to', 'generate file',
      'csv', 'json', 'excel', 'xlsx', 'txt', 'text file', 'spreadsheet',
      'save data', 'export data', 'download data'
    ];

    const message = userMessage.toLowerCase();
    return exportKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * Determine export format from user request
   */
  private determineFormat(userMessage: string): 'csv' | 'json' | 'excel' {
    const message = userMessage.toLowerCase();

    if (message.includes('excel') || message.includes('xlsx')) {
      return 'excel';
    }
    if (message.includes('csv') || message.includes('spreadsheet')) {
      return 'csv';
    }
    if (message.includes('json')) {
      return 'json';
    }
    return 'csv'; // Default to CSV for better compatibility
  }

  /**
   * Execute export process
   */
  async execute(userMessage: string): Promise<ExportResult> {
    try {
      logger.info('Exporter agent activated', { message: userMessage });

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, 'Preparing data export...');

      // Get the last extraction result from context
      const extractedData = this.context.lastExtractionResult;

      // Enhanced logging for debugging - log the actual data structure to console
      console.log('[ExporterAgent] Raw extraction data:', extractedData);
      
      logger.info('Checking extracted data', {
        hasExtractedData: !!extractedData,
        hasData: extractedData?.data ? true : false,
        dataLength: extractedData?.data?.length || 0,
        dataType: typeof extractedData,
        dataKeys: extractedData ? Object.keys(extractedData) : []
      });

      // More flexible data detection - try to find data in different formats
      let dataToExport: any[] = [];
      let metadata: any = null;

      if (extractedData) {
        // Method 1: Standard format with .data property
        if (extractedData.data && Array.isArray(extractedData.data) && extractedData.data.length > 0) {
          dataToExport = extractedData.data;
          metadata = extractedData.metadata;
          console.log('[ExporterAgent] Found data in standard format:', dataToExport);
        }
        // Method 2: Direct array
        else if (Array.isArray(extractedData) && extractedData.length > 0) {
          dataToExport = extractedData;
          console.log('[ExporterAgent] Found data as direct array:', dataToExport);
        }
        // Method 3: Results property (from smart extraction)
        else if (extractedData.results && Array.isArray(extractedData.results)) {
          // Flatten results from smart extraction
          dataToExport = extractedData.results.flatMap((result: any) => result.data || []);
          metadata = extractedData.metadata;
          console.log('[ExporterAgent] Found data in results format:', dataToExport);
        }
        // Method 4: Try to extract from any property that looks like data
        else {
          for (const [key, value] of Object.entries(extractedData)) {
            if (Array.isArray(value) && value.length > 0) {
              dataToExport = value;
              console.log(`[ExporterAgent] Found data in property '${key}':`, dataToExport);
              break;
            }
          }
        }
      }

      // Log the final data we'll export to console (as requested by user)
      console.log('[ExporterAgent] Final data to export:', JSON.stringify(dataToExport, null, 2));

      if (dataToExport.length === 0) {
        const errorMsg = 'No data available to export. Please extract data first.';
        logger.error('Export failed - no data', { extractedData, dataToExport });
        console.error('[ExporterAgent] Export failed - no exportable data found');
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
        return {
          success: false,
          filename: 'no_data',
          error: errorMsg,
        };
      }

      // Determine export format
      const format = this.determineFormat(userMessage);

      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Converting data to ${format.toUpperCase()} format...`);

      // Create standardized extraction data format for export service
      const standardizedData = {
        success: true,
        data: dataToExport,
        metadata: metadata || {
          source_url: 'AI_GENERATED',
          extraction_timestamp: Date.now(),
          total_items: dataToExport.length,
          extraction_method: 'flexible_detection',
          confidence_score: 0.8,
        },
      };

      // Use the DataExportService for proper export handling
      logger.info('Exporting data using DataExportService', {
        format,
        dataCount: dataToExport.length,
        hasMetadata: !!metadata
      });

      // Prepare export options
      const exportOptions: ExportOptions = {
        format: format,
        filename: this.generateFilename(standardizedData.metadata, format),
        includeMetadata: true,
        customHeaders: this.generateCustomHeaders(dataToExport)
      };

      // Use the DataExportService to export the data
      const exportResult = await this.exportService.exportData(standardizedData, exportOptions);

      if (exportResult.success) {
        const successMsg = `Successfully exported ${dataToExport.length} items to ${exportResult.filename}`;
        console.log('[ExporterAgent] Export successful:', successMsg);
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, successMsg);

        return {
          success: true,
          filename: exportResult.filename,
          downloadUrl: exportResult.downloadUrl,
          size: exportResult.size,
        };
      } else {
        console.error('[ExporterAgent] Export failed:', exportResult.error);
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, exportResult.error || 'Export failed');
        return {
          success: false,
          filename: exportResult.filename,
          error: exportResult.error,
        };
      }

    } catch (error) {
      const errorMsg = `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error('Export execution failed', error);
      console.error('[ExporterAgent] Export execution failed:', error);
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);

      return {
        success: false,
        filename: 'export_failed',
        error: errorMsg,
      };
    }
  }

  /**
   * Generate custom headers for better data presentation
   */
  private generateCustomHeaders(data: any[]): Record<string, string> {
    if (data.length === 0) return {};

    const customHeaders: Record<string, string> = {};
    const firstItem = data[0];

    Object.keys(firstItem).forEach(key => {
      // Convert snake_case and camelCase to Title Case
      const formatted = key
        .replace(/_/g, ' ')
        .replace(/([A-Z])/g, ' $1')
        .replace(/\b\w/g, l => l.toUpperCase())
        .trim();

      customHeaders[key] = formatted;
    });

    return customHeaders;
  }

  /**
   * Generate appropriate filename
   */
  private generateFilename(metadata: any, format: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    let baseName = 'extracted_data';

    if (metadata?.source_url) {
      try {
        const domain = new URL(metadata.source_url).hostname.replace(/[^a-zA-Z0-9]/g, '_');
        baseName = domain;
      } catch {
        // Keep default name if URL parsing fails
      }
    }

    // Use appropriate file extension
    let extension = format;
    if (format === 'excel') extension = 'xlsx';

    return `${baseName}_${timestamp}.${extension}`;
  }

  /**
   * Create download using browser APIs (service worker compatible)
   */
  private async createDownload(content: string, filename: string, mimeType: string): Promise<void> {
    try {
      // Convert content to data URL (works in service workers)
      const base64Content = btoa(unescape(encodeURIComponent(content)));
      const dataUrl = `data:${mimeType};base64,${base64Content}`;

      // Use Chrome downloads API
      if (typeof chrome !== 'undefined' && chrome?.downloads) {
        await new Promise<void>((resolve, reject) => {
          chrome.downloads.download({
            url: dataUrl,
            filename: filename,
            saveAs: true,
          }, (downloadId) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              logger.info('Download created successfully', { downloadId, filename });
              resolve();
            }
          });
        });
      } else {
        throw new Error('Chrome downloads API not available');
      }

    } catch (error) {
      logger.error('Download creation failed', error);
      throw error;
    }
  }
}
